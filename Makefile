.PHONY: build seed migration train pro seeder migrater mock prices-copy

build:
	go build -o server main.go

seed:
	go run cmd/seed/*.go
	
migration:
	go run cmd/migration/*.go

train:
	rm -rf precache/bot/*
	go run cmd/train/*.go
	
pro:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o server main.go

seeder:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o seed cmd/seed/*.go

migrater:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o migration cmd/migration/*.go
	
mock:
	go generate ./internal/contract/...

prices-copy:
	@echo "--> Finding a running pod for deployment 'homestead-deployment'..."
	@# Find the name of the first running pod that belongs to the deployment.
	@# We use '$$' to escape the '$' for Make, so the shell sees a single '$'.
	@POD_NAME=$$(kubectl get pods -n homestead -l app=homestead -o jsonpath='{.items[0].metadata.name}'); \
	if [ -z "$$POD_NAME" ]; then \
		echo "Error: No pod found with label app=homestead. Is the deployment running?"; \
		exit 1; \
	fi; \
	echo "--> Copying all files from 'precache/stockprices' to pod '$$POD_NAME' folder '/data'..."; \
	kubectl -n homestead cp precache/stockprices/. "$$POD_NAME:/data";
	@# rsync -avz -e 'kubectl exec -i' precache/stockprices/. "$$POD_NAME:/data"
	@echo "--> Done."