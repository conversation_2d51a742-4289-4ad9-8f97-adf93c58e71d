package main

import (
	"context"
	"database/sql"
	"flag"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/spf13/viper"
	"gonum.org/v1/plot"
	"gonum.org/v1/plot/plotter"
	"gonum.org/v1/plot/plotutil"
	"gonum.org/v1/plot/vg"

	// Database drivers
	_ "github.com/lib/pq"                 // PostgreSQL driver
	_ "github.com/mithrandie/csvq-driver" // CSV driver
)

// StockData represents a single day's stock data
type StockData struct {
	Date   time.Time
	Open   float64
	High   float64
	Low    float64
	Close  float64
	Volume float64
}

func main() {
	// Parse command line arguments
	var stockCode string
	flag.StringVar(&stockCode, "stock", "", "Stock code to generate price/volume chart for (required)")
	flag.Parse()

	if stockCode == "" {
		fmt.Println("Usage: go run main.go -stock=<STOCK_CODE>")
		fmt.Println("Example: go run main.go -stock=VIC")
		return
	}

	// Convert to uppercase for consistency
	stockCode = strings.ToUpper(stockCode)

	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		panic(fmt.Sprintf("Failed to read config: %v", err))
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		panic(fmt.Sprintf("Failed to unmarshal config: %v", err))
	}

	// Database homestead (for stock validation)
	parsedURL, err := url.Parse(cfg.Database.URL)
	if err != nil {
		panic(fmt.Sprintf("Failed to parse database URL: %v", err))
	}
	db, err := sql.Open(parsedURL.Scheme, cfg.Database.URL)
	if err != nil {
		panic(fmt.Sprintf("Failed to open database: %v", err))
	}
	defer func() {
		if err := db.Close(); err != nil {
			fmt.Printf("Error closing database: %v\n", err)
		}
	}()

	// Database for stock prices (CSV files)
	dbPrice, err := sql.Open("csvq", cfg.StoragePath.StockPrices)
	if err != nil {
		panic(fmt.Sprintf("Failed to open price database: %v", err))
	}
	defer func() {
		if err := dbPrice.Close(); err != nil {
			fmt.Printf("Error closing price database: %v\n", err)
		}
	}()

	// Validate stock exists in database
	if err := validateStock(db, stockCode); err != nil {
		fmt.Printf("Stock validation failed: %v\n", err)
		return
	}

	// Get stock data for the last month
	now := time.Now()
	oneMonthAgo := now.AddDate(0, -1, 0)

	fmt.Printf("Generating price/volume chart for stock %s from %s to %s\n",
		stockCode, oneMonthAgo.Format("2006-01-02"), now.Format("2006-01-02"))

	stockData, err := getStockData(dbPrice, stockCode, oneMonthAgo.Unix(), now.Unix())
	if err != nil {
		fmt.Printf("Failed to get stock data: %v\n", err)
		return
	}

	if len(stockData) == 0 {
		fmt.Printf("No stock data found for stock %s in the last month\n", stockCode)
		return
	}

	fmt.Printf("Found %d stock records\n", len(stockData))

	// Generate price chart
	err = generatePriceChart(stockCode, stockData)
	if err != nil {
		fmt.Printf("Failed to generate price chart: %v\n", err)
		return
	}

	// Generate volume chart
	err = generateVolumeChart(stockCode, stockData)
	if err != nil {
		fmt.Printf("Failed to generate volume chart: %v\n", err)
		return
	}

	// Generate combined chart
	err = generateCombinedChart(stockCode, stockData)
	if err != nil {
		fmt.Printf("Failed to generate combined chart: %v\n", err)
		return
	}

	fmt.Printf("Charts generated successfully!\n")
	fmt.Printf("- Price chart: %s-price-chart.png\n", strings.ToLower(stockCode))
	fmt.Printf("- Volume chart: %s-volume-chart.png\n", strings.ToLower(stockCode))
	fmt.Printf("- Combined chart: %s-combined-chart.png\n", strings.ToLower(stockCode))
}

// validateStock checks if the stock exists in the database
func validateStock(db *sql.DB, stockCode string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stmt, err := db.PrepareContext(ctx, `SELECT id FROM stocks WHERE id = $1`)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	var id string
	err = stmt.QueryRowContext(ctx, stockCode).Scan(&id)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("stock %s not found in database", stockCode)
		}
		return fmt.Errorf("failed to query stock: %w", err)
	}

	return nil
}

// getStockData retrieves complete stock data for a stock within the specified time range
func getStockData(dbStockPrice *sql.DB, stockCode string, from int64, to int64) ([]StockData, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Query the CSV file for the stock
	query := fmt.Sprintf("SELECT `date`, `open`, `high`, `low`, `close`, `volume` FROM `%s.csv` WHERE date >= ? AND date <= ? ORDER BY date ASC", stockCode)
	stmt, err := dbStockPrice.PrepareContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	rs, err := stmt.QueryContext(ctx, from, to)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rs.Close()

	var stockData []StockData
	for rs.Next() {
		var dateUnix int64
		var open, high, low, close, volume float64

		if err = rs.Scan(&dateUnix, &open, &high, &low, &close, &volume); err != nil {
			return nil, fmt.Errorf("failed to scan stock data: %w", err)
		}

		// Convert prices from stored format (multiplied by 1000) to actual values
		stockData = append(stockData, StockData{
			Date:   time.Unix(dateUnix, 0),
			Open:   open / 1000.0,
			High:   high / 1000.0,
			Low:    low / 1000.0,
			Close:  close / 1000.0,
			Volume: volume, // Volume is not multiplied by 1000
		})
	}

	if err = rs.Err(); err != nil {
		return nil, fmt.Errorf("error iterating results: %w", err)
	}

	fmt.Printf("Retrieved %d stock records\n", len(stockData))
	return stockData, nil
}

// generatePriceChart creates a line chart showing OHLC prices over time
func generatePriceChart(stockCode string, data []StockData) error {
	if len(data) == 0 {
		return fmt.Errorf("no data to plot")
	}

	p := plot.New()
	p.Title.Text = fmt.Sprintf("%s Stock Prices - Last Month", stockCode)
	p.X.Label.Text = "Date"
	p.Y.Label.Text = "Price"

	// Prepare data for plotting
	openPts := make(plotter.XYs, len(data))
	highPts := make(plotter.XYs, len(data))
	lowPts := make(plotter.XYs, len(data))
	closePts := make(plotter.XYs, len(data))

	for i, d := range data {
		x := float64(d.Date.Unix())
		openPts[i].X = x
		openPts[i].Y = d.Open
		highPts[i].X = x
		highPts[i].Y = d.High
		lowPts[i].X = x
		lowPts[i].Y = d.Low
		closePts[i].X = x
		closePts[i].Y = d.Close
	}

	// Create line plots
	openLine, err := plotter.NewLine(openPts)
	if err != nil {
		return fmt.Errorf("failed to create open line: %w", err)
	}
	openLine.Color = plotutil.Color(0)
	openLine.Width = vg.Points(1)

	highLine, err := plotter.NewLine(highPts)
	if err != nil {
		return fmt.Errorf("failed to create high line: %w", err)
	}
	highLine.Color = plotutil.Color(1)
	highLine.Width = vg.Points(1)

	lowLine, err := plotter.NewLine(lowPts)
	if err != nil {
		return fmt.Errorf("failed to create low line: %w", err)
	}
	lowLine.Color = plotutil.Color(2)
	lowLine.Width = vg.Points(1)

	closeLine, err := plotter.NewLine(closePts)
	if err != nil {
		return fmt.Errorf("failed to create close line: %w", err)
	}
	closeLine.Color = plotutil.Color(3)
	closeLine.Width = vg.Points(2)

	// Add lines to plot
	p.Add(openLine, highLine, lowLine, closeLine)
	p.Legend.Add("Open", openLine)
	p.Legend.Add("High", highLine)
	p.Legend.Add("Low", lowLine)
	p.Legend.Add("Close", closeLine)

	// Set X-axis to show dates properly
	p.X.Tick.Marker = plot.TimeTicks{Format: "01-02"}

	// Save the plot
	filename := fmt.Sprintf("%s-price-chart.png", strings.ToLower(stockCode))
	if err := p.Save(10*vg.Inch, 6*vg.Inch, filename); err != nil {
		return fmt.Errorf("failed to save price chart: %w", err)
	}

	return nil
}

// generateVolumeChart creates a bar chart showing trading volume over time
func generateVolumeChart(stockCode string, data []StockData) error {
	if len(data) == 0 {
		return fmt.Errorf("no data to plot")
	}

	p := plot.New()
	p.Title.Text = fmt.Sprintf("%s Trading Volume - Last Month", stockCode)
	p.X.Label.Text = "Date"
	p.Y.Label.Text = "Volume"

	// Prepare data for plotting
	volumeValues := make(plotter.Values, len(data))
	for i, d := range data {
		volumeValues[i] = d.Volume
	}

	// Create bar chart
	bars, err := plotter.NewBarChart(volumeValues, vg.Points(20))
	if err != nil {
		return fmt.Errorf("failed to create volume bars: %w", err)
	}
	bars.Color = plotutil.Color(4)

	// Add bars to plot
	p.Add(bars)

	// Set X-axis to show dates properly
	p.X.Tick.Marker = plot.TimeTicks{Format: "01-02"}

	// Save the plot
	filename := fmt.Sprintf("%s-volume-chart.png", strings.ToLower(stockCode))
	if err := p.Save(10*vg.Inch, 6*vg.Inch, filename); err != nil {
		return fmt.Errorf("failed to save volume chart: %w", err)
	}

	return nil
}

// generateCombinedChart creates a combined chart with price and volume
func generateCombinedChart(stockCode string, data []StockData) error {
	if len(data) == 0 {
		return fmt.Errorf("no data to plot")
	}

	p := plot.New()
	p.Title.Text = fmt.Sprintf("%s Stock Price & Volume - Last Month", stockCode)
	p.X.Label.Text = "Date"
	p.Y.Label.Text = "Price"

	// Prepare price data
	closePts := make(plotter.XYs, len(data))
	for i, d := range data {
		closePts[i].X = float64(d.Date.Unix())
		closePts[i].Y = d.Close
	}

	// Create close price line
	closeLine, err := plotter.NewLine(closePts)
	if err != nil {
		return fmt.Errorf("failed to create close line: %w", err)
	}
	closeLine.Color = plotutil.Color(0)
	closeLine.Width = vg.Points(2)

	// Add price line to plot
	p.Add(closeLine)
	p.Legend.Add("Close Price", closeLine)

	// Set X-axis to show dates properly
	p.X.Tick.Marker = plot.TimeTicks{Format: "01-02"}

	// Save the plot
	filename := fmt.Sprintf("%s-combined-chart.png", strings.ToLower(stockCode))
	if err := p.Save(10*vg.Inch, 6*vg.Inch, filename); err != nil {
		return fmt.Errorf("failed to save combined chart: %w", err)
	}

	return nil
}
