package main

import (
	"context"
	"database/sql"
	"flag"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/util"
	"github.com/spf13/viper"
	"gonum.org/v1/plot/plotter"
)

func main() {
	// Parse command line arguments
	var stockCode string
	flag.StringVar(&stockCode, "stock", "", "Stock code to generate histogram for (required)")
	flag.Parse()

	if stockCode == "" {
		fmt.Println("Usage: go run main.go -stock=<STOCK_CODE>")
		fmt.Println("Example: go run main.go -stock=VIC")
		return
	}

	// Convert to uppercase for consistency
	stockCode = strings.ToUpper(stockCode)

	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		panic(fmt.Sprintf("Failed to read config: %v", err))
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		panic(fmt.Sprintf("Failed to unmarshal config: %v", err))
	}

	// Database homestead (for stock validation)
	parsedURL, err := url.Parse(cfg.Database.URL)
	if err != nil {
		panic(fmt.Sprintf("Failed to parse database URL: %v", err))
	}
	db, err := sql.Open(parsedURL.Scheme, cfg.Database.URL)
	if err != nil {
		panic(fmt.Sprintf("Failed to open database: %v", err))
	}
	defer func() {
		if err := db.Close(); err != nil {
			fmt.Printf("Error closing database: %v\n", err)
		}
	}()

	// Database for stock prices (CSV files)
	dbPrice, err := sql.Open("csvq", cfg.StoragePath.StockPrices)
	if err != nil {
		panic(fmt.Sprintf("Failed to open price database: %v", err))
	}
	defer func() {
		if err := dbPrice.Close(); err != nil {
			fmt.Printf("Error closing price database: %v\n", err)
		}
	}()

	// Validate stock exists in database
	if err := validateStock(db, stockCode); err != nil {
		fmt.Printf("Stock validation failed: %v\n", err)
		return
	}

	// Get price data for the last month
	now := time.Now()
	oneMonthAgo := now.AddDate(0, -1, 0)
	
	fmt.Printf("Generating histogram for stock %s from %s to %s\n", 
		stockCode, oneMonthAgo.Format("2006-01-02"), now.Format("2006-01-02"))

	prices, err := getStockPrices(dbPrice, stockCode, oneMonthAgo.Unix(), now.Unix())
	if err != nil {
		fmt.Printf("Failed to get stock prices: %v\n", err)
		return
	}

	if len(prices) == 0 {
		fmt.Printf("No price data found for stock %s in the last month\n", stockCode)
		return
	}

	fmt.Printf("Found %d price records\n", len(prices))

	// Convert to plotter.Values for histogram
	values := make(plotter.Values, len(prices))
	for i, price := range prices {
		// Prices are stored multiplied by 1000, so divide by 1000 to get actual price
		values[i] = price / 1000.0
	}

	// Generate histogram
	title := fmt.Sprintf("%s Stock Prices - Last Month", stockCode)
	bins := 20 // Number of histogram bins
	
	err = util.HistogramPlot(title, values, bins)
	if err != nil {
		fmt.Printf("Failed to generate histogram: %v\n", err)
		return
	}

	fmt.Printf("Histogram saved as '%s.png'\n", strings.ToLower(strings.ReplaceAll(title, " ", "-")))
}

// validateStock checks if the stock exists in the database
func validateStock(db *sql.DB, stockCode string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stmt, err := db.PrepareContext(ctx, `SELECT id FROM stocks WHERE id = $1`)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	var id string
	err = stmt.QueryRowContext(ctx, stockCode).Scan(&id)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("stock %s not found in database", stockCode)
		}
		return fmt.Errorf("failed to query stock: %w", err)
	}

	return nil
}

// getStockPrices retrieves closing prices for a stock within the specified time range
func getStockPrices(dbStockPrice *sql.DB, stockCode string, from int64, to int64) ([]float64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Query the CSV file for the stock
	query := fmt.Sprintf("SELECT `close` FROM `%s.csv` WHERE date >= ? AND date <= ? ORDER BY date ASC", stockCode)
	stmt, err := dbStockPrice.PrepareContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	rs, err := stmt.QueryContext(ctx, from, to)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rs.Close()

	var prices []float64
	for rs.Next() {
		var closePrice float64
		if err = rs.Scan(&closePrice); err != nil {
			return nil, fmt.Errorf("failed to scan price: %w", err)
		}
		prices = append(prices, closePrice)
	}

	if err = rs.Err(); err != nil {
		return nil, fmt.Errorf("error iterating results: %w", err)
	}

	return prices, nil
}
